.popoverContent-ZvwtMgOV {
  padding: 16px;
  min-width: 320px;
  max-width: 380px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);
}
.popoverTitle-JPCpFP3I {
  padding: 0 0 12px 0;
  border-bottom: 1px solid rgba(37, 99, 235, 0.08);
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}
.infoItem-6xYKGZ8Q {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 0 8px 0;
  position: relative;
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(37, 99, 235, 0.04);
}
.infoItem-6xYKGZ8Q:hover {
  background: rgba(37, 99, 235, 0.06);
  border-color: rgba(37, 99, 235, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.08);
}
.infoItem-6xYKGZ8Q.email-i9QUimb9:hover {
  background: rgba(24, 144, 255, 0.04);
}
.infoItem-6xYKGZ8Q.phone-IA4B8_HE:hover {
  background: rgba(82, 196, 26, 0.04);
}
.infoItem-6xYKGZ8Q.register-VT8PxSJM:hover {
  background: rgba(114, 46, 209, 0.04);
}
.infoItem-6xYKGZ8Q.lastLogin-VCyens7z:hover {
  background: rgba(250, 140, 22, 0.04);
}
.infoItem-6xYKGZ8Q.team-lHzntWgR:hover {
  background: rgba(19, 194, 194, 0.04);
}
.iconWrapper-5vz1D_9p {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border: 1px solid rgba(37, 99, 235, 0.12);
  flex-shrink: 0;
  transition: all 0.2s ease;
}
.icon-_OERA8Hx {
  font-size: 14px;
  font-weight: 600;
}
.infoContent-vsma-SV- {
  flex: 1 1;
  min-width: 0;
}
.label-ehWFee0h {
  display: block;
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 4px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.value-Zl-mRo9s {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #1f2937;
  word-break: break-all;
}
.trigger-AR_BTHjV {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 4px 6px;
  margin: -4px -6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trigger-AR_BTHjV:hover {
  background: rgba(24, 144, 255, 0.06);
  transform: scale(1.05);
}
.questionIcon-hRheYLsO {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}
.questionIcon-hRheYLsO:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}
.settingIcon-ijupiTrh {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}
.settingIcon-ijupiTrh:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}
@media (max-width: 768px) {
  .popoverContent-ZvwtMgOV {
    min-width: 280px;
    max-width: 320px;
  }
  .infoItem-6xYKGZ8Q {
    gap: 10px;
    padding: 8px 0;
  }
  .iconWrapper-5vz1D_9p {
    width: 24px;
    height: 24px;
  }
  .icon-_OERA8Hx {
    font-size: 12px;
  }
  .label-ehWFee0h {
    font-size: 11px;
  }
  .value-Zl-mRo9s {
    font-size: 13px;
  }
}
@media (max-width: 576px) {
  .popoverContent-ZvwtMgOV {
    min-width: 260px;
    max-width: 300px;
  }
  .popoverTitle-JPCpFP3I {
    font-size: 13px;
    padding: 6px 0 10px 0;
    margin-bottom: 10px;
  }
  .infoItem-6xYKGZ8Q {
    gap: 8px;
    padding: 6px 0;
  }
  .iconWrapper-5vz1D_9p {
    width: 22px;
    height: 22px;
  }
  .icon-_OERA8Hx {
    font-size: 11px;
  }
  .label-ehWFee0h {
    font-size: 10px;
    margin-bottom: 2px;
  }
  .value-Zl-mRo9s {
    font-size: 12px;
  }
}
@keyframes fadeIn-8DvHJTuh {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.popoverContent-ZvwtMgOV {
  -webkit-animation: fadeIn 0.2s ease-out;
  animation: fadeIn-8DvHJTuh 0.2s ease-out;
}
.divider-vyN6VYw3 {
  margin: 8px 0;
  border-color: #f0f0f0;
}
.value-Zl-mRo9s .ant-typography-copy {
  color: #8c8c8c;
  margin-left: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
}
.value-Zl-mRo9s:hover .ant-typography-copy {
  opacity: 1;
  color: #1890ff;
}
/*# sourceMappingURL=src_pages_personal-center_index_tsx-async.css.map*/