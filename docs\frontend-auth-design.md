# 前端身份验证和团队切换功能设计文档

## 目录

1. [概述](#概述)
2. [详细代码流程文档](#详细代码流程文档)
3. [技术实现细节](#技术实现细节)
4. [可视化流程图](#可视化流程图)
5. [代码示例](#代码示例)
6. [安全注意事项](#安全注意事项)
7. [最佳实践](#最佳实践)

## 概述

本文档详细描述了 TeamAuth 系统前端身份验证和团队切换功能的完整设计和实现。系统采用基于 JWT Token 的单阶段认证机制，支持用户登录、团队选择、团队切换和会话管理等核心功能。

### 核心特性

- **验证码登录系统**：使用邮箱验证码进行安全登录，支持自动注册
- **双层 Token 机制**：用户级 Token 和团队级 Token，支持灵活的权限控制
- **无缝团队切换**：在不同团队间快速切换，保持用户体验流畅
- **自动会话管理**：Token 自动刷新、过期处理和会话状态同步
- **路由守卫保护**：基于认证状态的页面访问控制

## 详细代码流程文档

### 1. 用户身份验证流程

#### 1.1 登录流程（从登录到团队选择）

```mermaid
sequenceDiagram
    participant U as 用户
    participant LC as 登录组件
    participant AS as AuthService
    participant API as 后端API
    participant TM as TokenManager
    participant GS as 全局状态

    U->>LC: 输入邮箱
    LC->>AS: sendVerificationCode(email)
    AS->>API: POST /auth/send-code
    API-->>AS: 验证码发送成功
    AS-->>LC: 返回成功状态
    
    U->>LC: 输入验证码
    LC->>AS: login({email, code})
    AS->>API: POST /auth/login
    API-->>AS: {user, teams[], token}
    AS->>TM: setToken(userToken)
    AS-->>LC: 返回登录响应
    
    LC->>GS: 更新 currentUser 和 currentTeam
    LC->>LC: 根据团队数量决定跳转
    
    alt 无团队
        LC->>U: 跳转到个人中心
    else 有团队
        LC->>U: 跳转到个人中心（团队列表）
    end
```

#### 1.2 详细登录处理逻辑

<augment_code_snippet path="frontend/src/pages/user/login/index.tsx" mode="EXCERPT">
````typescript
// 处理登录/注册
const handleLogin = useCallback(async (values: LoginRequest) => {
  setLoading(true);
  try {
    const response = await AuthService.login(values);

    // 登录成功后停止倒计时
    setCountdown(0);

    // 登录成功后，刷新 initialState
    await setInitialState((prevState) => ({
      ...prevState,
      currentUser: response.user,
      currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,
    }));

    // 根据团队数量进行不同的跳转处理
    if (response.teams.length === 0) {
      // 没有团队，跳转到个人中心页面
      history.push('/personal-center');
    } else {
      // 有团队（无论一个还是多个），都跳转到个人中心整合页面
      history.push('/personal-center', { teams: response.teams });
    }
  } catch (error) {
    // 错误处理由响应拦截器统一处理
  } finally {
    setLoading(false);
  }
}, [setInitialState]);
````
</augment_code_snippet>

### 2. 团队切换工作流程

#### 2.1 团队切换核心流程

团队切换是系统的核心功能之一，允许用户在不同团队间无缝切换。

<augment_code_snippet path="frontend/src/pages/personal-center/TeamListCard.tsx" mode="EXCERPT">
````typescript
/**
 * 团队切换处理函数
 *
 * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。
 * 包括权限检查、API调用、状态更新、页面跳转等步骤。
 */
const handleTeamSwitch = async (teamId: number, teamName: string) => {
  if (!initialState?.currentUser) {
    return;
  }

  try {
    setSwitchingTeamId(teamId);

    // 当前团队检查 - 避免重复切换
    if (teamId === actualCurrentTeamId) {
      history.push('/dashboard');
      return;
    }

    // 执行团队切换API调用
    const response = await AuthService.selectTeam({ teamId });
````
</augment_code_snippet>

#### 2.2 团队切换API调用和状态管理

<augment_code_snippet path="frontend/src/services/auth.ts" mode="EXCERPT">
````typescript
/**
 * 选择团队
 *
 * 用户登录后选择要进入的团队。选择团队后会获得包含团队信息的新Token。
 * 这个新Token包含团队上下文，允许用户访问团队相关的功能和数据。
 */
static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
  const response = await apiRequest.post<LoginResponse>('/auth/select-team', data);

  // 自动保存团队Token到本地存储
  if (response.data.token) {
    TokenManager.setToken(response.data.token);
  }

  return response.data;
}
````
</augment_code_snippet>

### 3. 错误处理和边缘情况

#### 3.1 Token 过期处理

系统通过响应拦截器统一处理 Token 过期和认证错误：

<augment_code_snippet path="frontend/src/utils/request.ts" mode="EXCERPT">
````typescript
// 响应拦截器 - 错误处理
(error: any) => {
  if (error.response) {
    const { status } = error.response;
    if (status === 401) {
      // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
      const currentPath = window.location.pathname;
      const isDashboardRelated =
        currentPath.startsWith('/dashboard') ||
        currentPath.startsWith('/team');

      if (isDashboardRelated) {
        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
        return Promise.reject(error);
      }

      // 其他页面立即处理认证错误
      TokenManager.clearToken();
      getMessageApi().error('登录已过期，请重新登录');
      if (window.location.pathname !== '/user/login') {
        history.push('/user/login');
      }
    }
  }
}
````
</augment_code_snippet>

#### 3.2 会话管理和令牌刷新机制

<augment_code_snippet path="frontend/src/services/auth.ts" mode="EXCERPT">
````typescript
/**
 * 刷新Token
 *
 * 使用当前有效的Token获取新的Token，延长会话时间。
 * 通常在Token即将过期时自动调用，也可以手动调用来延长会话。
 */
static async refreshToken(): Promise<LoginResponse> {
  const response = await apiRequest.post<LoginResponse>('/auth/refresh-token');

  // 自动更新本地存储的Token
  if (response.data.token) {
    TokenManager.setToken(response.data.token);
  }

  return response.data;
}
````
</augment_code_snippet>

## 技术实现细节

### 1. 前端组件架构和数据流

#### 1.1 核心组件结构

```
src/
├── pages/
│   ├── user/login/           # 登录页面组件
│   └── personal-center/      # 个人中心和团队管理
├── services/
│   └── auth.ts              # 认证服务API封装
├── utils/
│   ├── request.ts           # HTTP请求和Token管理
│   └── tokenUtils.ts        # Token解析工具
└── app.tsx                  # 应用入口和路由守卫
```

#### 1.2 状态管理模式

系统使用 UmiJS 的 `initialState` 进行全局状态管理：

```typescript
interface InitialState {
  currentUser?: UserInfo;      // 当前登录用户信息
  currentTeam?: TeamInfo;      // 当前选择的团队信息
  fetchUserInfo: () => Promise<UserInfo | undefined>;
  fetchTeamInfo: () => Promise<TeamInfo | undefined>;
}
```

### 2. API 端点规范和请求/响应格式

#### 2.1 认证相关API端点

| 端点 | 方法 | 描述 | 权限要求 |
|------|------|------|----------|
| `/auth/send-code` | POST | 发送验证码 | 公开 |
| `/auth/login` | POST | 用户登录 | 公开 |
| `/auth/select-team` | POST | 选择团队 | 用户Token |
| `/auth/switch-team` | POST | 切换团队 | 用户Token |
| `/auth/refresh-token` | POST | 刷新Token | 用户Token |
| `/auth/logout` | POST | 用户登出 | 用户Token |

#### 2.2 请求/响应格式

**登录请求格式：**
```typescript
interface LoginRequest {
  email: string;    // 用户邮箱
  code: string;     // 6位验证码
}
```

**登录响应格式：**
```typescript
interface LoginResponse {
  token: string;                    // JWT Token
  user: UserInfo;                   // 用户信息
  teams: TeamInfo[];               // 用户所属团队列表
  teamSelectionSuccess?: boolean;   // 团队选择是否成功
  currentTeam?: TeamInfo;          // 当前选择的团队
}
```

### 3. 本地存储和会话存储使用

#### 3.1 Token存储策略

<augment_code_snippet path="frontend/src/utils/request.ts" mode="EXCERPT">
````typescript
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  static getToken(): string | null {
    try {
      return localStorage.getItem(TokenManager.TOKEN_KEY);
    } catch (error) {
      console.warn('无法访问localStorage:', error);
      return null;
    }
  }

  static setToken(token: string): void {
    try {
      localStorage.setItem(TokenManager.TOKEN_KEY, token);
    } catch (error) {
      console.error('无法保存Token到localStorage:', error);
      throw new Error('Token保存失败，请检查浏览器存储设置');
    }
  }
}
````
</augment_code_snippet>

#### 3.2 Token信息解析

<augment_code_snippet path="frontend/src/utils/tokenUtils.ts" mode="EXCERPT">
````typescript
/**
 * 解析 JWT Token 的 payload 部分
 */
export function parseJwtPayload(token: string): any | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    // 处理base64url编码
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const paddedBase64 = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=');
    
    const decoded = atob(paddedBase64);
    return JSON.parse(decoded);
  } catch (error) {
    console.error('Token解析失败:', error);
    return null;
  }
}
````
</augment_code_snippet>

### 4. 路由守卫和权限控制

#### 4.1 应用级路由守卫

<augment_code_snippet path="frontend/src/app.tsx" mode="EXCERPT">
````typescript
/**
 * 页面切换时的路由守卫
 */
onPageChange: () => {
  const { location } = history;

  // 检查用户是否已登录且当前页面是否需要身份验证
  if (
    !initialState?.currentUser &&
    !['/user/login', '/user/register'].includes(location.pathname) &&
    !location.pathname.startsWith('/invite/')
  ) {
    // 用户未登录且访问受保护页面，跳转到登录页
    history.push('/user/login');
  }
},
````
</augment_code_snippet>

#### 4.2 初始状态获取和验证

<augment_code_snippet path="frontend/src/app.tsx" mode="EXCERPT">
````typescript
// 检查当前路径是否为公开页面（不需要身份验证）
if (!['/user/login', '/user/register'].includes(location.pathname)) {
  try {
    // 身份验证检查
    if (!AuthService.isLoggedIn()) {
      return { fetchUserInfo, fetchTeamInfo };
    }

    // 用户信息获取
    const currentUser = await fetchUserInfo();
    if (!currentUser) {
      return { fetchUserInfo, fetchTeamInfo };
    }

    // 团队信息获取
    const currentTeam = await fetchTeamInfo();
````
</augment_code_snippet>

## 可视化流程图

### 1. 用户身份验证流程图

```mermaid
flowchart TD
    A[用户访问登录页] --> B[输入邮箱地址]
    B --> C[点击发送验证码]
    C --> D[后端发送验证码到邮箱]
    D --> E[用户输入验证码]
    E --> F[提交登录表单]
    F --> G{验证码是否正确?}

    G -->|否| H[显示错误信息]
    H --> E

    G -->|是| I[后端验证用户]
    I --> J{用户是否存在?}

    J -->|否| K[自动创建新用户]
    K --> L[生成用户Token]

    J -->|是| L
    L --> M[查询用户团队列表]
    M --> N{用户是否有团队?}

    N -->|否| O[跳转到个人中心]
    N -->|是| P[跳转到团队选择页面]

    O --> Q[用户可以创建或加入团队]
    P --> R[用户选择团队进入]
```

### 2. 团队切换流程图

```mermaid
flowchart TD
    A[用户在团队列表页面] --> B[点击切换团队按钮]
    B --> C{是否为当前团队?}

    C -->|是| D[直接跳转到仪表盘]

    C -->|否| E[设置切换状态loading]
    E --> F[调用团队选择API]
    F --> G{API调用是否成功?}

    G -->|否| H[显示错误信息]
    H --> I[清除loading状态]

    G -->|是| J[更新本地Token]
    J --> K[验证切换结果]
    K --> L{团队ID是否匹配?}

    L -->|否| M[显示切换失败]
    M --> I

    L -->|是| N[显示成功消息]
    N --> O[记录用户选择历史]
    O --> P[异步更新全局状态]
    P --> Q[跳转到团队仪表盘]
    Q --> I
```

### 3. 系统架构图

```mermaid
graph TB
    subgraph "前端应用"
        A[登录组件] --> B[AuthService]
        C[团队切换组件] --> B
        D[路由守卫] --> B
        B --> E[TokenManager]
        B --> F[HTTP请求拦截器]
        E --> G[localStorage]
        F --> H[API请求]
    end

    subgraph "后端服务"
        H --> I[认证控制器]
        I --> J[认证服务]
        J --> K[JWT工具]
        J --> L[会话服务]
        K --> M[Token生成/验证]
        L --> N[会话缓存]
    end

    subgraph "数据存储"
        J --> O[用户数据库]
        J --> P[团队数据库]
        N --> Q[Redis缓存]
    end

    subgraph "安全层"
        R[JWT过滤器] --> I
        S[CORS配置] --> I
        T[安全配置] --> I
    end
```

### 4. 身份验证和团队上下文状态转换图

```mermaid
stateDiagram-v2
    [*] --> 未登录

    未登录 --> 登录中 : 输入验证码
    登录中 --> 已登录_无团队 : 登录成功(无团队)
    登录中 --> 已登录_有团队 : 登录成功(有团队)
    登录中 --> 未登录 : 登录失败

    已登录_无团队 --> 团队选择中 : 创建/加入团队
    已登录_有团队 --> 团队选择中 : 选择团队

    团队选择中 --> 已登录_团队上下文 : 团队选择成功
    团队选择中 --> 已登录_有团队 : 团队选择失败

    已登录_团队上下文 --> 团队切换中 : 切换团队
    团队切换中 --> 已登录_团队上下文 : 切换成功
    团队切换中 --> 已登录_团队上下文 : 切换失败

    已登录_无团队 --> 未登录 : 登出
    已登录_有团队 --> 未登录 : 登出
    已登录_团队上下文 --> 未登录 : 登出

    已登录_团队上下文 --> Token刷新中 : Token即将过期
    Token刷新中 --> 已登录_团队上下文 : 刷新成功
    Token刷新中 --> 未登录 : 刷新失败
```

## 代码示例

### 1. 关键前端组件和钩子

#### 1.1 登录表单组件

```typescript
// LoginFormComponent.tsx
import React from 'react';
import { Form, Input, Button, message } from 'antd';
import { MailOutlined, SafetyOutlined } from '@ant-design/icons';

interface LoginFormProps {
  form: FormInstance;
  handleLogin: (values: LoginRequest) => Promise<void>;
  handleSendCode: () => Promise<void>;
  sendingCode: boolean;
  countdown: number;
  loading: boolean;
}

const LoginFormComponent: React.FC<LoginFormProps> = ({
  form,
  handleLogin,
  handleSendCode,
  sendingCode,
  countdown,
  loading,
}) => {
  return (
    <Form
      form={form}
      name="login"
      onFinish={handleLogin}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱地址' },
          { type: 'email', message: '请输入有效的邮箱地址' },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="邮箱地址"
          autoComplete="email"
        />
      </Form.Item>

      <Form.Item>
        <Input.Group compact>
          <Form.Item
            name="code"
            noStyle
            rules={[
              { required: true, message: '请输入验证码' },
              { len: 6, message: '验证码为6位数字' },
            ]}
          >
            <Input
              prefix={<SafetyOutlined />}
              placeholder="6位验证码"
              style={{ width: '60%' }}
              autoComplete="one-time-code"
            />
          </Form.Item>
          <Button
            style={{ width: '40%' }}
            onClick={handleSendCode}
            loading={sendingCode}
            disabled={countdown > 0}
          >
            {countdown > 0 ? `${countdown}s` : '发送验证码'}
          </Button>
        </Input.Group>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
        >
          登录 / 注册
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginFormComponent;
```

#### 1.2 团队切换钩子

```typescript
// useTeamSwitch.ts
import { useState, useCallback } from 'react';
import { history, useModel } from '@umijs/max';
import { message } from 'antd';
import { AuthService } from '@/services/auth';
import { recordTeamSelection } from '@/utils/teamSelectionUtils';

export const useTeamSwitch = () => {
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);
  const { initialState, setInitialState } = useModel('@@initialState');

  const switchTeam = useCallback(async (teamId: number, teamName: string) => {
    if (!initialState?.currentUser) {
      message.error('用户未登录');
      return false;
    }

    try {
      setSwitchingTeamId(teamId);

      // 调用团队切换API
      const response = await AuthService.selectTeam({ teamId });

      if (response.teamSelectionSuccess && response.currentTeam?.id === teamId) {
        message.success(`已切换到团队：${teamName}`);

        // 记录用户选择历史
        recordTeamSelection(initialState.currentUser.id, teamId);

        // 异步更新全局状态
        Promise.all([
          initialState.fetchUserInfo(),
          initialState.fetchTeamInfo()
        ]).then(([currentUser, currentTeam]) => {
          if (currentTeam && currentTeam.id === teamId) {
            setInitialState({
              ...initialState,
              currentUser,
              currentTeam
            });
          }
        }).catch((error) => {
          console.error('更新 initialState 失败:', error);
        });

        // 跳转到团队仪表盘
        history.push('/dashboard');
        return true;
      } else {
        message.error('团队切换失败，请重试');
        return false;
      }
    } catch (error) {
      console.error('团队切换失败:', error);
      return false;
    } finally {
      setSwitchingTeamId(null);
    }
  }, [initialState, setInitialState]);

  return {
    switchTeam,
    switchingTeamId,
    isSwitching: switchingTeamId !== null,
  };
};
```

### 2. API 服务函数

#### 2.1 完整的认证服务类

```typescript
// services/auth.ts
import { apiRequest } from '@/utils/request';
import { TokenManager } from '@/utils/request';

export interface LoginRequest {
  email: string;
  code: string;
}

export interface LoginResponse {
  token: string;
  user: UserInfo;
  teams: TeamInfo[];
  teamSelectionSuccess?: boolean;
  currentTeam?: TeamInfo;
}

export class AuthService {
  /**
   * 发送验证码
   */
  static async sendVerificationCode(email: string): Promise<void> {
    await apiRequest.post('/auth/send-code', { email });
  }

  /**
   * 用户登录
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/login', data);

    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 选择团队
   */
  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/select-team', data);

    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 切换团队
   */
  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/switch-team', data);

    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 刷新Token
   */
  static async refreshToken(): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/refresh-token');

    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await apiRequest.post('/auth/logout');
    } finally {
      TokenManager.clearToken();
    }
  }

  /**
   * 检查用户是否已登录
   */
  static isLoggedIn(): boolean {
    const token = TokenManager.getToken();
    return !!token;
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<UserInfo | null> {
    try {
      const response = await apiRequest.get<UserInfo>('/users/current');
      return response.data;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取当前团队信息
   */
  static async getCurrentTeam(): Promise<TeamInfo | null> {
    try {
      const response = await apiRequest.get<TeamInfo>('/teams/current');
      return response.data;
    } catch (error) {
      return null;
    }
  }
}
```

### 3. 身份验证守卫和路由保护

#### 3.1 高阶组件路由守卫

```typescript
// components/AuthGuard.tsx
import React, { useEffect } from 'react';
import { history, useModel } from '@umijs/max';
import { Spin } from 'antd';
import { AuthService } from '@/services/auth';

interface AuthGuardProps {
  children: React.ReactNode;
  requireTeam?: boolean; // 是否需要团队上下文
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, requireTeam = false }) => {
  const { initialState, loading } = useModel('@@initialState');

  useEffect(() => {
    // 检查用户登录状态
    if (!loading && !initialState?.currentUser) {
      history.push('/user/login');
      return;
    }

    // 检查团队上下文要求
    if (requireTeam && !initialState?.currentTeam) {
      history.push('/personal-center');
      return;
    }
  }, [loading, initialState, requireTeam]);

  // 显示加载状态
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 用户未登录
  if (!initialState?.currentUser) {
    return null;
  }

  // 需要团队上下文但未选择团队
  if (requireTeam && !initialState?.currentTeam) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;
```

#### 3.2 路由配置示例

```typescript
// config/routes.ts
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      { path: '/user/login', component: './user/login' },
      { path: '/user/register', component: './user/register' },
    ],
  },
  {
    path: '/personal-center',
    component: './personal-center',
    wrappers: ['@/components/AuthGuard'], // 需要登录
  },
  {
    path: '/dashboard',
    component: './dashboard',
    wrappers: ['@/components/AuthGuard'], // 需要登录和团队上下文
    requireTeam: true,
  },
  {
    path: '/teams',
    routes: [
      {
        path: '/teams/current',
        component: './teams/current',
        wrappers: ['@/components/AuthGuard'],
        requireTeam: true,
      },
    ],
  },
];
```

### 4. 团队上下文提供者和消费者

#### 4.1 团队上下文Provider

```typescript
// contexts/TeamContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useModel } from '@umijs/max';
import { getTokenInfo } from '@/utils/tokenUtils';

interface TeamContextType {
  currentTeam: TeamInfo | null;
  isTeamCreator: boolean;
  hasTeamPermission: (permission: string) => boolean;
  refreshTeamInfo: () => Promise<void>;
}

const TeamContext = createContext<TeamContextType | undefined>(undefined);

export const TeamProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { initialState } = useModel('@@initialState');
  const [isTeamCreator, setIsTeamCreator] = useState(false);

  useEffect(() => {
    // 从Token中获取团队创建者状态
    const tokenInfo = getTokenInfo();
    setIsTeamCreator(tokenInfo.isCreator || false);
  }, [initialState?.currentTeam]);

  const hasTeamPermission = (permission: string): boolean => {
    if (!initialState?.currentTeam) return false;

    switch (permission) {
      case 'manage_team':
        return isTeamCreator;
      case 'manage_members':
        return isTeamCreator;
      case 'view_data':
        return true; // 所有团队成员都可以查看数据
      default:
        return false;
    }
  };

  const refreshTeamInfo = async (): Promise<void> => {
    if (initialState?.fetchTeamInfo) {
      await initialState.fetchTeamInfo();
    }
  };

  const value: TeamContextType = {
    currentTeam: initialState?.currentTeam || null,
    isTeamCreator,
    hasTeamPermission,
    refreshTeamInfo,
  };

  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
};

export const useTeamContext = (): TeamContextType => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeamContext must be used within a TeamProvider');
  }
  return context;
};
```

#### 4.2 团队权限钩子

```typescript
// hooks/useTeamPermissions.ts
import { useMemo } from 'react';
import { useTeamContext } from '@/contexts/TeamContext';

export const useTeamPermissions = () => {
  const { isTeamCreator, hasTeamPermission } = useTeamContext();

  const permissions = useMemo(() => ({
    canManageTeam: hasTeamPermission('manage_team'),
    canManageMembers: hasTeamPermission('manage_members'),
    canViewData: hasTeamPermission('view_data'),
    canInviteMembers: isTeamCreator,
    canDeleteTeam: isTeamCreator,
  }), [isTeamCreator, hasTeamPermission]);

  return permissions;
};
```

## 安全注意事项

### 1. Token 安全

#### 1.1 Token 存储安全
- **localStorage 使用**：Token 存储在 localStorage 中，避免了 CSRF 攻击，但需要防范 XSS 攻击
- **Token 格式**：使用标准的 JWT 格式，包含签名验证
- **自动清理**：Token 过期或无效时自动清理本地存储

#### 1.2 Token 传输安全
- **HTTPS 传输**：所有 API 请求都通过 HTTPS 进行，确保传输过程中的安全性
- **Bearer Token**：使用标准的 Authorization: Bearer 头部格式
- **请求拦截**：自动在每个请求中注入 Token，避免手动处理

### 2. 会话管理安全

#### 2.1 会话验证
- **双重验证**：前端 Token 验证 + 后端会话验证
- **活跃检查**：定期检查会话活跃状态，自动清理过期会话
- **并发控制**：限制同一用户的并发会话数量

#### 2.2 自动登出机制
- **Token 过期**：Token 过期时自动登出并跳转到登录页
- **长时间无操作**：检测用户长时间无操作时提示续期或自动登出
- **异常检测**：检测到异常访问模式时强制登出

### 3. 前端安全防护

#### 3.1 XSS 防护
- **输入验证**：所有用户输入都进行严格验证和转义
- **CSP 策略**：配置内容安全策略，防止恶意脚本执行
- **安全编码**：使用 React 的安全特性，避免 dangerouslySetInnerHTML

#### 3.2 CSRF 防护
- **Token 机制**：使用 JWT Token 而非 Cookie，天然防护 CSRF
- **SameSite 策略**：如果使用 Cookie，配置适当的 SameSite 属性
- **Referer 检查**：后端验证请求来源

## 最佳实践

### 1. 代码组织最佳实践

#### 1.1 模块化设计
- **服务分离**：认证逻辑独立封装在 AuthService 中
- **工具函数**：Token 操作和解析功能独立为工具模块
- **组件复用**：登录表单、团队选择等组件可复用

#### 1.2 错误处理统一化
- **响应拦截器**：统一处理 HTTP 错误和认证错误
- **错误边界**：使用 React Error Boundary 捕获组件错误
- **用户友好提示**：提供清晰的错误信息和操作指导

### 2. 性能优化最佳实践

#### 2.1 状态管理优化
- **按需获取**：只在需要时获取用户和团队信息
- **缓存策略**：合理使用缓存减少不必要的 API 调用
- **状态同步**：确保全局状态与 Token 状态保持同步

#### 2.2 网络请求优化
- **请求去重**：避免重复的认证请求
- **超时处理**：设置合理的请求超时时间
- **重试机制**：对于网络错误实现智能重试

### 3. 用户体验最佳实践

#### 3.1 加载状态管理
- **加载指示器**：在登录、团队切换等操作时显示加载状态
- **防重复提交**：防止用户重复点击提交按钮
- **操作反馈**：及时提供操作成功或失败的反馈

#### 3.2 导航和跳转
- **智能跳转**：根据用户状态智能决定跳转目标
- **历史记录**：保持浏览器历史记录的正确性
- **深链接支持**：支持直接访问特定页面并自动处理认证

### 4. 开发和调试最佳实践

#### 4.1 日志和监控
- **详细日志**：记录关键操作的详细日志
- **错误监控**：集成错误监控服务，及时发现问题
- **性能监控**：监控认证相关操作的性能指标

#### 4.2 测试策略
- **单元测试**：为认证服务和工具函数编写单元测试
- **集成测试**：测试完整的认证流程
- **端到端测试**：模拟真实用户操作进行测试

---

本文档提供了 TeamAuth 系统前端身份验证和团队切换功能的完整设计和实现指南。通过遵循这些设计原则和最佳实践，可以构建一个安全、高效、用户友好的身份验证系统。

