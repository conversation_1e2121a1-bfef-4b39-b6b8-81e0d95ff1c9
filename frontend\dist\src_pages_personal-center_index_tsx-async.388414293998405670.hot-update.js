globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/UserInfoPopover.module.css?modules": function(module, exports, __mako_require__) {},
        "src/pages/personal-center/UserInfoPopover.module.css?asmodule": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            "";
            var _default = {
                "trigger": `trigger-AR_BTHjV`,
                "email": `email-i9QUimb9`,
                "phone": `phone-IA4B8_HE`,
                "label": `label-ehWFee0h`,
                "value": `value-Zl-mRo9s`,
                "popoverContent": `popoverContent-ZvwtMgOV`,
                "divider": `divider-vyN6VYw3`,
                "lastLogin": `lastLogin-VCyens7z`,
                "popoverTitle": `popoverTitle-JPCpFP3I`,
                "settingIcon": `settingIcon-ijupiTrh`,
                "team": `team-lHzntWgR`,
                "iconWrapper": `iconWrapper-5vz1D_9p`,
                "icon": `icon-_OERA8Hx`,
                "fadeIn": `fadeIn-8DvHJTuh`,
                "register": `register-VT8PxSJM`,
                "infoItem": `infoItem-6xYKGZ8Q`,
                "infoContent": `infoContent-vsma-SV-`,
                "questionIcon": `questionIcon-hRheYLsO`
            };
        }
    }
}, function(runtime) {
    runtime._h = '15051799322150654464';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.388414293998405670.hot-update.js.map