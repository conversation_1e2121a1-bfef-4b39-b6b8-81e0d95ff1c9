globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _todo = __mako_require__("src/services/todo.ts");
            var _paginationUtils = __mako_require__("src/utils/paginationUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = ()=>{
                _s();
                /**
   * 响应式检测
   */ const { useBreakpoint } = _antd.Grid;
                const screens = useBreakpoint();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)('pending');
                const [searchText, setSearchText] = (0, _react.useState)('');
                // 分页功能
                const { pagination, updateTotal } = (0, _paginationUtils.usePagination)({
                    defaultPageSize: 10,
                    pageSizeOptions: [
                        '5',
                        '10',
                        '20',
                        '50'
                    ],
                    showTotal: (total, range)=>`共 ${total} 条待办事项，显示第 ${range[0]}-${range[1]} 条`
                });
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            console.log('TodoManagement: 开始获取TODO数据');
                            // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                            const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                                console.error('获取TODO列表失败:', error);
                                return [];
                            });
                            const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                                console.error('获取TODO统计失败:', error);
                                return {
                                    highPriorityCount: 0,
                                    mediumPriorityCount: 0,
                                    lowPriorityCount: 0,
                                    totalCount: 0,
                                    completedCount: 0,
                                    completionPercentage: 0
                                };
                            });
                            const [todos, stats] = await Promise.all([
                                todosPromise,
                                statsPromise
                            ]);
                            console.log('TodoManagement: 获取到TODO列表:', todos);
                            console.log('TodoManagement: 获取到统计数据:', stats);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据时发生未知错误:', error);
                            setError('获取TODO数据失败，请刷新页面重试');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = (0, _react.useMemo)(()=>{
                    return (personalTasks || []).filter((task)=>{
                        // 根据标签过滤
                        if (activeTab === 'pending' && task.status === 1) return false;
                        if (activeTab === 'completed' && task.status === 0) return false;
                        // 根据搜索文本过滤
                        if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                        return true;
                    });
                }, [
                    personalTasks,
                    activeTab,
                    searchText
                ]);
                // 更新总数
                _react.default.useEffect(()=>{
                    updateTotal(filteredPersonalTasks.length);
                }, [
                    filteredPersonalTasks.length,
                    updateTotal
                ]);
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) return;
                        const newStatus = task.status === 0 ? 1 : 0;
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                        }
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        align: "center",
                        gap: 8,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#2563eb'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 242,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                style: {
                                    color: '#1f2937',
                                    fontWeight: 600
                                },
                                children: "待办事项/任务列表"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 243,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 241,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        borderRadius: 16,
                        height: 'fit-content',
                        minHeight: '500px',
                        border: '1px solid rgba(37, 99, 235, 0.08)',
                        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
                        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)'
                    },
                    headStyle: {
                        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
                        paddingBottom: 12,
                        background: 'rgba(37, 99, 235, 0.02)'
                    },
                    bodyStyle: {
                        padding: screens.md ? '16px' : '12px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: screens.md ? [
                                16,
                                0
                            ] : [
                                12,
                                8
                            ],
                            style: {
                                marginBottom: screens.md ? 16 : 12
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 16,
                                    lg: 18,
                                    xl: 20,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                        placeholder: "搜索任务...",
                                        allowClear: true,
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 272,
                                            columnNumber: 21
                                        }, void 0),
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        style: {
                                            width: '100%'
                                        },
                                        size: screens.md ? "middle" : "small"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 269,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 268,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 8,
                                    lg: 6,
                                    xl: 4,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 283,
                                            columnNumber: 19
                                        }, void 0),
                                        onClick: ()=>{
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                            setTodoModalVisible(true);
                                        },
                                        size: screens.md ? "middle" : "small",
                                        block: true,
                                        children: screens.md ? '添加任务' : '添加'
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 281,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 280,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 264,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: (key)=>setActiveTab(key),
                            size: screens.md ? "middle" : "small",
                            style: {
                                marginBottom: screens.md ? 8 : 6
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "全部"
                                }, "all", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 304,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "待处理"
                                }, "pending", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 305,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "已完成"
                                }, "completed", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 306,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 298,
                            columnNumber: 7
                        }, this),
                        error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "TODO数据加载失败",
                            description: error,
                            type: "error",
                            showIcon: true,
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 313,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: loading,
                            children: [
                                activeTab === 'pending' && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    style: {
                                        marginBottom: screens.md ? 16 : 12
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        gap: screens.md ? 12 : 8,
                                        wrap: "wrap",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    background: '#fff2f0',
                                                    border: '1px solid #ffccc7',
                                                    borderRadius: 6,
                                                    padding: screens.md ? '8px 12px' : '6px 10px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: screens.md ? 8 : 6,
                                                    minWidth: screens.md ? 100 : 80
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: screens.md ? 8 : 6,
                                                            height: screens.md ? 8 : 6,
                                                            borderRadius: '50%',
                                                            background: '#ff4d4f'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 339,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: screens.md ? 12 : 11,
                                                            color: '#8c8c8c',
                                                            marginRight: 4
                                                        },
                                                        children: "高优先级"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 347,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            fontWeight: 600,
                                                            color: '#cf1322'
                                                        },
                                                        children: todoStats.highPriorityCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 354,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 327,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    background: '#fffbe6',
                                                    border: '1px solid #ffe58f',
                                                    borderRadius: 6,
                                                    padding: '8px 12px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 8,
                                                    minWidth: 100
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 8,
                                                            height: 8,
                                                            borderRadius: '50%',
                                                            background: '#faad14'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 376,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: '#8c8c8c',
                                                            marginRight: 4
                                                        },
                                                        children: "中优先级"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 384,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                            color: '#d48806'
                                                        },
                                                        children: todoStats.mediumPriorityCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 387,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 364,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    background: '#fafafa',
                                                    border: '1px solid #d9d9d9',
                                                    borderRadius: 6,
                                                    padding: '8px 12px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 8,
                                                    minWidth: 100
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 8,
                                                            height: 8,
                                                            borderRadius: '50%',
                                                            background: '#8c8c8c'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 405,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: '#8c8c8c',
                                                            marginRight: 4
                                                        },
                                                        children: "低优先级"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 413,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                            color: '#595959'
                                                        },
                                                        children: todoStats.lowPriorityCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 416,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 393,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 325,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 324,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProList, {
                                    dataSource: filteredPersonalTasks,
                                    pagination: {
                                        current: pagination.current,
                                        pageSize: pagination.pageSize,
                                        total: filteredPersonalTasks.length,
                                        showSizeChanger: pagination.showSizeChanger,
                                        showQuickJumper: pagination.showQuickJumper,
                                        showTotal: pagination.showTotal,
                                        pageSizeOptions: pagination.pageSizeOptions,
                                        onChange: pagination.onChange,
                                        onShowSizeChange: pagination.onShowSizeChange
                                    },
                                    renderItem: (item)=>{
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: "todo-item",
                                            style: {
                                                padding: '12px 16px',
                                                marginBottom: 8,
                                                borderRadius: 12,
                                                background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
                                                opacity: item.status === 1 ? 0.7 : 1,
                                                borderLeft: `4px solid ${item.status === 1 ? '#059669' : item.priority === 3 ? '#dc2626' : item.priority === 2 ? '#d97706' : '#64748b'}`,
                                                boxShadow: '0 2px 8px rgba(37, 99, 235, 0.04)',
                                                border: '1px solid rgba(37, 99, 235, 0.06)',
                                                transition: 'all 0.2s ease'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 12,
                                                style: {
                                                    width: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        children: [
                                                            item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                justify: "center",
                                                                style: {
                                                                    width: 22,
                                                                    height: 22,
                                                                    borderRadius: '50%',
                                                                    background: '#52c41a'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                    style: {
                                                                        color: '#fff',
                                                                        fontSize: 12
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 474,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 464,
                                                                columnNumber: 25
                                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 18,
                                                                    height: 18,
                                                                    borderRadius: '50%',
                                                                    border: `2px solid ${item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 479,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 2,
                                                                    height: 24,
                                                                    background: '#f0f0f0',
                                                                    marginTop: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 495,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 462,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        style: {
                                                            flex: 1
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 14,
                                                                    fontWeight: item.priority === 3 ? 500 : 'normal',
                                                                    textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                                    color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                                },
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 507,
                                                                columnNumber: 23
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                align: "center",
                                                                size: 6,
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 521,
                                                                        columnNumber: 25
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "创建于:",
                                                                            ' ',
                                                                            new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 527,
                                                                        columnNumber: 25
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 520,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 506,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                        trigger: [
                                                            'click'
                                                        ],
                                                        menu: {
                                                            items: [
                                                                {
                                                                    key: 'complete',
                                                                    label: item.status === 1 ? '标记未完成' : '标记完成',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                        style: {
                                                                            color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 544,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'edit',
                                                                    label: '编辑任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                                        style: {
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 556,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'delete',
                                                                    label: '删除任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                                        style: {
                                                                            color: '#ff4d4f'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 562,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    danger: true
                                                                }
                                                            ],
                                                            onClick: ({ key })=>{
                                                                if (key === 'complete') handleToggleTodoStatus(item.id);
                                                                else if (key === 'edit') {
                                                                    setEditingTodoId(item.id);
                                                                    todoForm.setFieldsValue({
                                                                        name: item.title,
                                                                        priority: item.priority
                                                                    });
                                                                    setTodoModalVisible(true);
                                                                } else if (key === 'delete') handleDeleteTodo(item.id);
                                                            }
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            type: "text",
                                                            size: "small",
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 586,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            style: {
                                                                width: 32,
                                                                height: 32
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 583,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 535,
                                                        columnNumber: 21
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 460,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 438,
                                            columnNumber: 17
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 423,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ModalForm, {
                                    title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                                    open: todoModalVisible,
                                    onOpenChange: (visible)=>{
                                        setTodoModalVisible(visible);
                                        if (!visible) {
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                        }
                                    },
                                    form: todoForm,
                                    layout: "vertical",
                                    onFinish: handleAddOrUpdateTodo,
                                    autoComplete: "off",
                                    width: 500,
                                    modalProps: {
                                        centered: true,
                                        destroyOnClose: true,
                                        maskClosable: true,
                                        keyboard: true,
                                        forceRender: false
                                    },
                                    submitter: {
                                        searchConfig: {
                                            submitText: editingTodoId ? '更新任务' : '创建任务',
                                            resetText: '取消'
                                        },
                                        submitButtonProps: {
                                            style: {
                                                background: '#1890ff',
                                                borderColor: '#1890ff',
                                                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                            },
                                            icon: editingTodoId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 630,
                                                columnNumber: 39
                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 630,
                                                columnNumber: 58
                                            }, void 0)
                                        },
                                        resetButtonProps: {
                                            style: {
                                                borderColor: '#d9d9d9'
                                            }
                                        },
                                        onReset: ()=>{
                                            setTodoModalVisible(false);
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                        }
                                    },
                                    preserve: false,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                            name: "name",
                                            label: "任务名称",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: '请输入任务名称'
                                                }
                                            ],
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                placeholder: "请输入任务名称",
                                                size: "large",
                                                style: {
                                                    borderRadius: 6
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 650,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 645,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                            name: "priority",
                                            label: "优先级",
                                            initialValue: 2,
                                            rules: [
                                                {
                                                    required: true,
                                                    message: '请选择优先级'
                                                }
                                            ],
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                size: "large",
                                                options: [
                                                    {
                                                        value: 3,
                                                        label: '高优先级'
                                                    },
                                                    {
                                                        value: 2,
                                                        label: '中优先级'
                                                    },
                                                    {
                                                        value: 1,
                                                        label: '低优先级'
                                                    }
                                                ],
                                                style: {
                                                    borderRadius: 6
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 663,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 657,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 597,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 321,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                    lineNumber: 239,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "/UJ2lw1RKQXeMCc65B2jVR6rQ8M=", true, function() {
                return [
                    _antd.Form.useForm,
                    _paginationUtils.usePagination
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '2723257408480516161';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.13660923752930836088.hot-update.js.map