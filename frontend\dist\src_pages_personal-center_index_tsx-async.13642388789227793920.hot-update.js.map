{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.13642388789227793920.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='7489340383887301519';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Spin,\n  Grid,\n  Flex,\n  Typography,\n} from 'antd';\nimport { ProCard, StatisticCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，\n * 采用响应式网格布局适配不同屏幕尺寸。包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题\n * 2. 显示人员数量统计 - 使用用户组图标，绿色主题\n * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题\n * 4. 显示告警数量统计 - 使用警告图标，红色主题\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 响应式布局特点：\n * - xs (< 576px): 2x2 网格布局，移动端优化\n * - sm (≥ 576px): 2x2 网格布局，小屏设备\n * - md (≥ 768px): 1x4 水平排列，平板优化\n * - lg+ (≥ 992px): 1x4 水平排列，桌面端\n * - 每个统计项都有语义化的图标和颜色主题\n * - 统一的卡片样式和高度\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 响应式检测\n   *\n   * 使用 Ant Design 的 Grid.useBreakpoint 检测当前屏幕尺寸，\n   * 根据不同断点调整统计卡片的布局方式\n   */\n  const { useBreakpoint } = Grid;\n  const screens = useBreakpoint();\n\n  /**\n   * 根据屏幕尺寸决定布局方向和列数\n   *\n   * - xs/sm: 垂直布局，2x2 网格\n   * - md+: 水平布局，1x4 排列\n   */\n  const getLayoutDirection = () => {\n    if (screens.md) return 'row';\n    return 'column';\n  };\n\n  /**\n   * 获取统计卡片的响应式样式\n   */\n  const getCardStyle = () => {\n    if (screens.md) {\n      // 桌面端：水平排列，等宽分布\n      return {\n        flex: 1,\n        minWidth: 0,\n      };\n    } else {\n      // 移动端：垂直排列，固定高度\n      return {\n        marginBottom: 12,\n      };\n    }\n  };\n\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <Flex align=\"center\" gap={8}>\n          <BarChartOutlined style={{ fontSize: 18, color: '#2563eb' }} />\n          <span style={{ color: '#1f2937', fontWeight: 600 }}>数据概览</span>\n        </Flex>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 16,\n        border: '1px solid rgba(37, 99, 235, 0.08)',\n        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',\n        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',\n      }}\n      headStyle={{\n        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',\n        paddingBottom: 12,\n        background: 'rgba(37, 99, 235, 0.02)',\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 使用 StatisticCard.Group 组件的响应式布局 */}\n          <StatisticCard.Group\n            direction={getLayoutDirection()}\n            style={{\n              gap: screens.md ? 16 : 12,\n            }}\n          >\n            {/* 车辆统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <CarOutlined\n                  style={{\n                    color: '#2563eb',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    车辆\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#2563eb',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.vehicles}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 人员统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <UsergroupAddOutlined\n                  style={{\n                    color: '#059669',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    人员\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#059669',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.personnel}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 预警统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <ExclamationCircleOutlined\n                  style={{\n                    color: '#d97706',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    预警\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#d97706',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.warnings}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 告警统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <AlertOutlined\n                  style={{\n                    color: '#dc2626',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    告警\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#dc2626',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.alerts}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n          </StatisticCard.Group>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCwVb;;;2BAAA;;;;;;0CAnVO;yCAOA;kDACgC;oFACI;yCACf;;;;;;;;;;YAG5B;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,eAAyB;;gBAC7B;;;;;GAKC,GACD,MAAM,EAAE,aAAa,EAAE,GAAG,UAAI;gBAC9B,MAAM,UAAU;gBAEhB;;;;;GAKC,GACD,MAAM,qBAAqB;oBACzB,IAAI,QAAQ,EAAE,EAAE,OAAO;oBACvB,OAAO;gBACT;gBAEA;;GAEC,GACD,MAAM,eAAe;oBACnB,IAAI,QAAQ,EAAE,EACZ,gBAAgB;oBAChB,OAAO;wBACL,MAAM;wBACN,UAAU;oBACZ;yBAEA,gBAAgB;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBAEJ;gBAEA;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC,UAAI;wBAAC,OAAM;wBAAS,KAAK;;0CACxB,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;gCAAK,OAAO;oCAAE,OAAO;oCAAW,YAAY;gCAAI;0CAAG;;;;;;;;;;;;oBAGxD,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;wBACR,YAAY;wBACZ,WAAW;oBACb;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;wBACf,YAAY;oBACd;oBACA,WAAW;wBACT,SAAS;oBACX;8BAEC,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC,4BAAa,CAAC,KAAK;4BAClB,WAAW;4BACX,OAAO;gCACL,KAAK,QAAQ,EAAE,GAAG,KAAK;4BACzB;;8CAGA,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,cAAA,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;0DAGA,2BAAC,kBAAW;gDACV,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;0DAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;kEAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,cAAA,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;0DAGA,2BAAC,2BAAoB;gDACnB,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;0DAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;kEAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;8CAOhC,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,cAAA,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;0DAGA,2BAAC,gCAAyB;gDACxB,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;0DAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;kEAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,cAAA,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;0DAGA,2BAAC,oBAAa;gDACZ,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;0DAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;kEAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC;eA7SM;iBAAA;gBA+SN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDxVD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}