{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.8013989697078297679.hot-update.js", "src/pages/personal-center/DataOverview.tsx", "src/services/todo.ts", "src/services/team.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16494110241775595293';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Spin,\n  Grid,\n  Flex,\n  Typography,\n} from 'antd';\nimport { ProCard, StatisticCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport { TeamService } from '@/services/team';\nimport { TodoService } from '@/services/todo';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，\n * 采用响应式网格布局适配不同屏幕尺寸。包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题\n * 2. 显示人员数量统计 - 使用用户组图标，绿色主题\n * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题\n * 4. 显示告警数量统计 - 使用警告图标，红色主题\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 响应式布局特点：\n * - xs (< 576px): 2x2 网格布局，移动端优化\n * - sm (≥ 576px): 2x2 网格布局，小屏设备\n * - md (≥ 768px): 1x4 水平排列，平板优化\n * - lg+ (≥ 992px): 1x4 水平排列，桌面端\n * - 每个统计项都有语义化的图标和颜色主题\n * - 统一的卡片样式和高度\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 响应式检测\n   *\n   * 使用 Ant Design 的 Grid.useBreakpoint 检测当前屏幕尺寸，\n   * 根据不同断点调整统计卡片的布局方式\n   */\n  const { useBreakpoint } = Grid;\n  const screens = useBreakpoint();\n\n  /**\n   * 根据屏幕尺寸决定布局方向和列数\n   *\n   * - xs/sm: 垂直布局，2x2 网格\n   * - md+: 水平布局，1x4 排列\n   */\n  const getLayoutDirection = () => {\n    if (screens.md) return 'row';\n    return 'column';\n  };\n\n  /**\n   * 获取统计卡片的响应式样式\n   */\n  const getCardStyle = () => {\n    if (screens.md) {\n      // 桌面端：水平排列，等宽分布\n      return {\n        flex: 1,\n        minWidth: 0,\n      };\n    } else {\n      // 移动端：垂直排列，固定高度\n      return {\n        marginBottom: 12,\n      };\n    }\n  };\n\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [teamCount, setTeamCount] = useState(0);\n  const [todoCount, setTodoCount] = useState(0);\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        // 获取个人统计数据\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n\n        // 获取团队数量\n        try {\n          const teams = await TeamService.getUserTeams();\n          setTeamCount(teams.length);\n        } catch (teamError) {\n          console.error('获取团队数据失败:', teamError);\n          setTeamCount(0);\n        }\n\n        // 获取待办事项数量\n        try {\n          const todoStats = await TodoService.getTodoStats();\n          setTodoCount(todoStats.totalCount || 0);\n        } catch (todoError) {\n          console.error('获取待办事项数据失败:', todoError);\n          setTodoCount(0);\n        }\n\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <Flex align=\"center\" gap={8}>\n          <BarChartOutlined style={{ fontSize: 18, color: '#2563eb' }} />\n          <span style={{ color: '#1f2937', fontWeight: 600 }}>数据概览</span>\n        </Flex>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 16,\n        border: '1px solid rgba(37, 99, 235, 0.08)',\n        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',\n        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',\n      }}\n      headStyle={{\n        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',\n        paddingBottom: 12,\n        background: 'rgba(37, 99, 235, 0.02)',\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 使用 StatisticCard.Group 组件的响应式布局 */}\n          <StatisticCard.Group\n            direction={getLayoutDirection()}\n            style={{\n              gap: screens.md ? 16 : 12,\n            }}\n          >\n            {/* 车辆统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <CarOutlined\n                  style={{\n                    color: '#2563eb',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    车辆\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#2563eb',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.vehicles}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 人员统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <UsergroupAddOutlined\n                  style={{\n                    color: '#059669',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    人员\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#059669',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.personnel}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 预警统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <ExclamationCircleOutlined\n                  style={{\n                    color: '#d97706',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    预警\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#d97706',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.warnings}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 告警统计 */}\n            <StatisticCard\n              style={getCardStyle()}\n            >\n              <Flex\n                align=\"center\"\n                justify=\"flex-start\"\n                style={{\n                  height: '100%',\n                  padding: screens.md ? '16px 12px' : '12px 8px',\n                  minHeight: screens.md ? 80 : 60,\n                }}\n              >\n                {/* 左侧图标 */}\n                <AlertOutlined\n                  style={{\n                    color: '#dc2626',\n                    fontSize: screens.md ? 28 : 24,\n                    marginRight: screens.md ? 16 : 12,\n                  }}\n                />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text\n                    style={{\n                      fontSize: screens.md ? 14 : 12,\n                      color: '#666',\n                      marginBottom: 4,\n                    }}\n                  >\n                    告警\n                  </Typography.Text>\n                  <Typography.Text\n                    style={{\n                      color: '#dc2626',\n                      fontSize: screens.md ? 36 : 28,\n                      fontWeight: 700,\n                      lineHeight: 1,\n                    }}\n                  >\n                    {personalStats.alerts}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n          </StatisticCard.Group>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n", "/**\n * TODO服务\n */\n\nimport type {\n  CreateTodoRequest,\n  TodoResponse,\n  TodoStatsResponse,\n  UpdateTodoRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(\n    id: number,\n    request: UpdateTodoRequest,\n  ): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(\n      `/todos/${id}`,\n      request,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport type {\n  CreateTeamRequest,\n  InviteMembersRequest,\n  PageRequest,\n  PageResponse,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  UpdateTeamRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { AuthService } from './auth';\n\n/**\n * 团队服务类\n *\n * 提供团队相关的所有API接口，包括：\n * - 团队创建和管理\n * - 团队成员管理\n * - 团队邀请功能\n * - 团队统计信息\n *\n * 权限说明：\n * - Account Token：用户级别操作（创建团队、获取用户团队列表）\n * - Team Token：团队级别操作（团队详情、成员管理等）\n * - 创建者权限：某些操作仅团队创建者可执行\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class TeamService {\n  /**\n   * 创建团队\n   *\n   * 创建新的团队，创建者自动成为团队管理员。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @param data 团队创建请求参数\n   * @param data.name 团队名称（必填，2-50字符）\n   * @param data.description 团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 创建的团队信息\n   * @throws 当团队名称重复或用户权限不足时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const newTeam = await TeamService.createTeam({\n   *   name: '开发团队',\n   *   description: '负责产品开发的团队'\n   * });\n   * console.log('团队创建成功:', newTeam.name);\n   * ```\n   */\n  static async createTeam(\n    data: CreateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表\n   *\n   * 获取当前用户所属的所有团队的基本信息。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @returns Promise<TeamDetailResponse[]> 团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeams();\n   * console.log('用户所属团队数量:', teams.length);\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, ID: ${team.id}`);\n   * });\n   * ```\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   *\n   * 获取当前用户所属的所有团队，包含每个团队的统计信息。\n   * 用于个人中心的团队列表展示。\n   *\n   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeamsWithStats();\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);\n   * });\n   * ```\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>(\n      '/teams?includeStats=true',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情\n   *\n   * 获取当前选择团队的详细信息。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @returns Promise<TeamDetailResponse> 团队详细信息\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teamDetail = await TeamService.getCurrentTeamDetail();\n   * console.log('当前团队:', teamDetail.name);\n   * console.log('团队描述:', teamDetail.description);\n   * console.log('成员数量:', teamDetail.memberCount);\n   * ```\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息\n   *\n   * 更新当前团队的基本信息，如名称、描述等。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 团队更新请求参数\n   * @param data.name 新的团队名称（可选）\n   * @param data.description 新的团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 更新后的团队信息\n   * @throws 当用户非团队创建者或团队名称重复时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const updatedTeam = await TeamService.updateCurrentTeam({\n   *   name: '新团队名称',\n   *   description: '更新后的团队描述'\n   * });\n   * console.log('团队信息更新成功');\n   * ```\n   */\n  static async updateCurrentTeam(\n    data: UpdateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>(\n      '/teams/current',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 删除指定团队（需要切换到该团队的 Team Token，仅创建者）\n   *\n   * @param teamId 要删除的团队ID\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteTeam(teamId: number): Promise<void> {\n    // 先切换到目标团队\n    await AuthService.selectTeam({ teamId });\n    // 然后删除团队\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 退出团队（清除团队上下文）\n   *\n   * 通过清除团队上下文来实现退出团队的效果。\n   * 用户将返回到用户级别的Token，可以重新选择团队。\n   *\n   * 注意：这个操作只是清除当前的团队上下文，用户仍然是团队成员，\n   * 只是需要重新选择团队。如果需要完全退出团队，需要团队创建者移除该成员。\n   *\n   * @returns Promise<string> 新的用户级别Token\n   * @throws 当用户未登录时抛出异常\n   */\n  static async leaveTeam(): Promise<string> {\n    return await AuthService.clearTeam();\n  }\n\n  /**\n   * 获取当前团队成员列表（简单数组格式）\n   *\n   * 获取当前团队的所有成员，返回简单数组格式。\n   * 内部调用分页接口并获取所有成员。\n   *\n   * @returns Promise<TeamMemberResponse[]> 团队成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const members = await TeamService.getCurrentTeamMembers();\n   * console.log('团队成员数量:', members.length);\n   * members.forEach(member => {\n   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);\n   * });\n   * ```\n   */\n  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {\n    const response = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000, // 获取大量数据以确保包含所有成员\n    });\n    return response?.list || [];\n  }\n\n  /**\n   * 获取当前团队成员列表（分页格式）\n   *\n   * 获取当前团队的成员列表，支持分页查询。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @param params 分页查询参数（可选）\n   * @param params.current 当前页码（默认1）\n   * @param params.pageSize 每页大小（默认10）\n   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const membersPage = await TeamService.getTeamMembers({\n   *   current: 1,\n   *   pageSize: 20\n   * });\n   *\n   * console.log('总成员数:', membersPage.total);\n   * console.log('当前页成员:', membersPage.list);\n   * ```\n   */\n  static async getTeamMembers(\n    params?: PageRequest,\n  ): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params,\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员\n   *\n   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 邀请请求参数\n   * @param data.emails 被邀请人的邮箱列表\n   * @returns Promise<void> 邀请发送成功时resolve\n   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.inviteMembers({\n   *   emails: ['<EMAIL>', '<EMAIL>']\n   * });\n   * console.log('邀请已发送');\n   * ```\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>(\n      '/teams/current/members/invite',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员\n   *\n   * 从当前团队中移除指定成员。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 要移除的成员ID\n   * @returns Promise<void> 移除成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.removeMember(123);\n   * console.log('成员已移除');\n   * ```\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(\n      `/teams/current/members/${memberId}`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 更新团队成员状态\n   *\n   * 更新团队成员的激活状态（启用/禁用）。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 成员ID\n   * @param isActive 是否激活（true=启用，false=禁用）\n   * @returns Promise<void> 更新成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * // 禁用成员\n   * await TeamService.updateMemberStatus(123, false);\n   * console.log('成员已禁用');\n   *\n   * // 启用成员\n   * await TeamService.updateMemberStatus(123, true);\n   * console.log('成员已启用');\n   * ```\n   */\n  static async updateMemberStatus(memberId: number, isActive: boolean): Promise<void> {\n    const response = await apiRequest.put<void>(\n      `/teams/current/members/${memberId}/status?isActive=${isActive}`,\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 获取团队统计信息\n   *\n   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。\n   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。\n   *\n   * @returns Promise<object> 团队统计信息\n   * @returns Promise<object>.memberCount 总成员数\n   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）\n   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const stats = await TeamService.getTeamStats();\n   * console.log('总成员数:', stats.memberCount);\n   * console.log('活跃成员数:', stats.activeMembers);\n   * console.log('最近活跃成员数:', stats.recentActivity);\n   * ```\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await TeamService.getCurrentTeamDetail();\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    const activeMembers = members.list.filter(\n      (member) => member.isActive,\n    ).length;\n    const recentActivity = members.list.filter((member) => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n\n}\n\n// 导出默认实例\nexport default TeamService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCiXb;;;2BAAA;;;;;;0CA5WO;yCAOA;kDACgC;sEACI;yCACf;yCACA;yCACA;;;;;;;;;;YA0B5B,MAAM,eAAyB;;gBAO7B,MAAM,EAAE,aAAa,EAAE,GAAG,UAAI;gBAC9B,MAAM,UAAU;gBAQhB,MAAM,qBAAqB;oBACzB,IAAI,QAAQ,EAAE,EAAE,OAAO;oBACvB,OAAO;gBACT;gBAKA,MAAM,eAAe;oBACnB,IAAI,QAAQ,EAAE,EAEZ,OAAO;wBACL,MAAM;wBACN,UAAU;oBACZ;yBAGA,OAAO;wBACL,cAAc;oBAChB;gBAEJ;gBAKA,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAE3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAG5D,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BAEF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BAGjB,IAAI;gCACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gCAC5C,aAAa,MAAM,MAAM;4BAC3B,EAAE,OAAO,WAAW;gCAClB,QAAQ,KAAK,CAAC,aAAa;gCAC3B,aAAa;4BACf;4BAGA,IAAI;gCACF,MAAM,YAAY,MAAM,iBAAW,CAAC,YAAY;gCAChD,aAAa,UAAU,UAAU,IAAI;4BACvC,EAAE,OAAO,WAAW;gCAClB,QAAQ,KAAK,CAAC,eAAe;gCAC7B,aAAa;4BACf;4BAEA,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,OACE,2BAAC,sBAAO;oBACN,OACE,2BAAC,UAAI;wBAAC,OAAM;wBAAS,KAAK;;4BACxB,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;4BAC1D,2BAAC;gCAAK,OAAO;oCAAE,OAAO;oCAAW,YAAY;gCAAI;0CAAG;;;;;;;;;;;;oBAGxD,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;wBACR,YAAY;wBACZ,WAAW;oBACb;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;wBACf,YAAY;oBACd;oBACA,WAAW;wBACT,SAAS;oBACX;8BAEC,aACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;+BAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,2BAAC,4BAAa,CAAC,KAAK;4BAClB,WAAW;4BACX,OAAO;gCACL,KAAK,QAAQ,EAAE,GAAG,KAAK;4BACzB;;gCAGA,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;4CAGA,2BAAC,kBAAW;gDACV,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;4CAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;oDAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;oDAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;gCAO/B,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;4CAGA,2BAAC,2BAAoB;gDACnB,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;4CAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;oDAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;oDAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;gCAOhC,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;4CAGA,2BAAC,gCAAyB;gDACxB,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;4CAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;oDAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;oDAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;gCAO/B,2BAAC,4BAAa;oCACZ,OAAO;8CAEP,2BAAC,UAAI;wCACH,OAAM;wCACN,SAAQ;wCACR,OAAO;4CACL,QAAQ;4CACR,SAAS,QAAQ,EAAE,GAAG,cAAc;4CACpC,WAAW,QAAQ,EAAE,GAAG,KAAK;wCAC/B;;4CAGA,2BAAC,oBAAa;gDACZ,OAAO;oDACL,OAAO;oDACP,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,aAAa,QAAQ,EAAE,GAAG,KAAK;gDACjC;;;;;;4CAGF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;oDAClE,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,OAAO;4DACP,cAAc;wDAChB;kEACD;;;;;;oDAGD,2BAAC,gBAAU,CAAC,IAAI;wDACd,OAAO;4DACL,OAAO;4DACP,UAAU,QAAQ,EAAE,GAAG,KAAK;4DAC5B,YAAY;4DACZ,YAAY;wDACd;kEAEC,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC;eApUM;iBAAA;gBAsUN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCxWF;;;2BAAA;;;;;4CAFc;;;;;;;;;YAEpB,MAAM;gBAIX,aAAa,eAAwC;oBACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;oBACtD,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,WAAW,OAA0B,EAAyB;oBACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;oBAC/D,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,WACX,EAAU,EACV,OAA0B,EACH;oBACvB,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,OAAO,EAAE,GAAG,CAAC,EACd;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,WAAW,EAAU,EAAiB;oBACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;gBACxC;gBAKA,aAAa,eAA2C;oBACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;oBACzD,OAAO,SAAS,IAAI;gBACtB;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCxBa,WAAW;2BAAX;;gBAoXb,OAA2B;2BAA3B;;;;;4CAxY2B;yCACC;;;;;;;;;YAmBrB,MAAM;gBAsBX,aAAa,WACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBACrE,OAAO,SAAS,IAAI;gBACtB;gBAoBA,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAmBA,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAmBA,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAuBA,aAAa,kBACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAiBA,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBASA,aAAa,WAAW,MAAc,EAAiB;oBAErD,MAAM,iBAAW,CAAC,UAAU,CAAC;wBAAE;oBAAO;oBAEtC,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAcA,aAAa,YAA6B;oBACxC,OAAO,MAAM,iBAAW,CAAC,SAAS;gBACpC;gBAoBA,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,YAAY,cAAc,CAAC;wBAChD,SAAS;wBACT,UAAU;oBACZ;oBACA,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;gBAC7B;gBAyBA,aAAa,eACX,MAAoB,EACuB;oBAC3C,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAqBA,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,iCACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAkBA,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CACtC,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBAEtC,OAAO,SAAS,IAAI;gBACtB;gBAwBA,aAAa,mBAAmB,QAAgB,EAAE,QAAiB,EAAiB;oBAClF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,uBAAuB,EAAE,SAAS,iBAAiB,EAAE,SAAS,CAAC;oBAElE,OAAO,SAAS,IAAI;gBACtB;gBAwBA,aAAa,eAIV;oBAGD,MAAM,aAAa,MAAM,YAAY,oBAAoB;oBACzD,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAEA,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CACvC,CAAC,SAAW,OAAO,QAAQ,EAC3B,MAAM;oBACR,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1C,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;YAGF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IHlZD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}